<template>
  <div class="app-container">
    <a-form :model="queryForm" ref="queryFormRef" layout="inline">
      <a-form-item name="title" label="标题">
        <a-input v-model="queryForm.title" placeholder="请输入标题" @press-enter="handleQuery" />
      </a-form-item>
      <a-form-item name="path" label="地址">
        <a-input v-model="queryForm.path" placeholder="请输入地址" @press-enter="handleQuery" />
      </a-form-item>
      <a-form-item>
        <a-space size="medium">
          <a-button v-has="'admin:sysApi:query'" type="primary" @click="handleQuery">
            搜索
          </a-button>
          <a-button @click="handlResetQuery"> 重置 </a-button>
        </a-space>
      </a-form-item>
    </a-form>

    <a-divider />

    <!-- Table -->
    <a-table
      :columns="columns"
      :data-source="tableData"
      :pagination="{
        'show-jumper': true,
        'show-page-size': true,
        total: pager.count,
        current: currentPage,
      }"
      row-key="id"
      @page-change="handlePageChange"
      @page-size-change="handlePageSizeChange"
    >
      <template #reqType="{ record }">
        <a-tag v-if="record.action.toLowerCase() === 'get'" color="cyan">{{ record.action }}</a-tag>
        <a-tag v-else-if="record.action.toLowerCase() === 'post'" color="gold">{{
          record.action
        }}</a-tag>
        <a-tag v-else-if="record.action.toLowerCase() === 'put'" color="green">{{
          record.action
        }}</a-tag>
        <a-tag v-else-if="record.action.toLowerCase() === 'delete'" color="pinkpurple">{{
          record.action
        }}</a-tag>
      </template>
      <template #createdAt="{ record }"> {{ parseTime(record.createdAt) }}</template>
      <template #action="{ record }">
        <a-button v-has="'admin:sysApi:edit'" type="text" @click="handleUpdate(record)"
          >修改
        </a-button>
      </template>
    </a-table>

    <!-- Drawer -->
    <a-drawer
      :visible="drawerVisible"
      :width="450"
      @before-ok="handleDrawerSubmit"
      @cancel="handleDrawerCancel"
    >
      <template #title> 修改接口管理 </template>
      <a-form :model="drawerForm" ref="drawerFormRef" :rules="rules">
        <a-form-item name="handle" label="Handle">
          <a-input v-model="drawerForm.handle" placeholder="请输入Handle" />
        </a-form-item>
        <a-form-item name="title" label="标题">
          <a-input v-model="drawerForm.title" placeholder="请输入标题" />
        </a-form-item>
        <a-form-item name="type" label="类型">
          <a-select v-model="drawerForm.type" placeholder="请选择类型" allowClear>
            <a-select-option>SYS</a-select-option>
            <a-select-option>BUS</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item name="action" label="方式">
          <a-select v-model="drawerForm.action" placeholder="请选择请求方式" allowClear>
            <a-select-option>GET</a-select-option>
            <a-select-option>POST</a-select-option>
            <a-select-option>PUT</a-select-option>
            <a-select-option>DELETE</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item name="path" label="路径">
          <a-input v-model="drawerForm.path" disabled />
        </a-form-item>
      </a-form>
    </a-drawer>
  </div>
</template>

<script setup lang="ts">
import { getCurrentInstance } from 'vue'
import { getSysApi, addSysApi, updateSysApi } from '@/api/admin/sys-api'
import { message, notification, Form } from 'ant-design-vue'
import { parseTime } from '@/utils/parseTime'

const instance = getCurrentInstance()
const { $refs } = instance!.proxy!

interface ApiForm {
  id?: string | number
  title?: string
  method?: string
  path?: string
  [key: string]: unknown
}

interface Pager {
  count: number
  pageIndex: number
  pageSize: number
}

// 分页当前页
const currentPage = ref<number>(1)

// Pager
const pager = reactive<Pager>({
  count: 0,
  pageIndex: 1,
  pageSize: 10,
})

// From 定义
const queryForm = reactive<ApiForm>({})
const drawerForm = reactive<ApiForm>({})

// From 校验规则
const rules = {
  title: [{ required: true, message: '请输入标题' }],
  method: [{ required: true, message: '请选择方式' }],
}

// 抽屉显示
const drawerVisible = ref(false)

// Table Columns
const columns = [
  { title: '标题', dataIndex: 'title' },
  { title: '请求类型', dataIndex: 'action', slotName: 'reqType' },
  { title: '请求信息', dataIndex: 'path' },
  { title: '创建时间', dataIndex: 'createdAt', slotName: 'createdAt' },
  { title: '操作', slotName: 'action' },
]

// Table Data
const tableData = ref([])
const drawerFormRef = ref<InstanceType<typeof Form>>()
// Drawer 确定事件
// 异步关闭Modal需要调用 done()
const handleDrawerSubmit = (done) => {
  drawerFormRef.value?.validate(async (valid) => {
    if (!valid) {
      let res
      if (!drawerForm.id) {
        const { code, msg } = await addSysApi(drawerForm)
        if (code === 200) {
          notification.success('新增成功')
        } else {
          notification.error(msg)
        }
      } else {
        const { code, msg } = await updateSysApi(drawerForm, drawerForm.id)
        if (code === 200) {
          notification.success('修改成功')
        } else {
          notification.error(msg)
        }
      }
      drawerVisible.value = false
      done()
      getSysApiInfo(pager)
    } else {
      message.error('表单校验失败')
      done(false)
    }
  })
}

// Drawer 关闭事件
const handleDrawerCancel = () => {
  drawerVisible.value = false
}

// 查询
const handleQuery = async () => {
  getSysApiInfo(queryForm)
  currentPage.value = 1
}

// 重置查询
const handlResetQuery = () => {
  $refs.queryFormRef.resetFields()

  handlePageChange(1)
}

// 修改
const handleUpdate = async (record) => {
  drawerVisible.value = true
  // updateSysApi(record);
  await nextTick()
  Object.assign(drawerForm, record)
}

/**
 * 分页改变
 * @param {Number} [page]
 */
const handlePageChange = (page) => {
  pager.pageIndex = page

  // 修改当前页码
  currentPage.value = page
  getSysApiInfo({ ...pager, ...queryForm })
}

// 每页数据量
const handlePageSizeChange = (pageSize) => {
  pager.pageSize = pageSize
  getSysApiInfo({ ...pager, ...queryForm })
}

// 获取接口信息
const getSysApiInfo = async (params = {}) => {
  const { data, code, msg } = await getSysApi(params)
  if (code === 200) {
    tableData.value = data.list
    Object.assign(pager, { count: data.count, pageIndex: data.pageIndex, pageSize: data.pageSize })
  } else {
    notification.error(msg)
  }
}

onMounted(() => {
  getSysApiInfo(pager)
})
</script>

<style lang="scss">
.pagination {
  margin-top: 12px;
  display: flex;
  justify-content: flex-end;
}

// Table 操作列样式
.arco-table-th:last-child > span {
  margin-left: 15px;
}
</style>
